@echo off
echo Fixing key Vue files...

echo Creating basic template for error pages...
echo ^<template^>^<div^>^<h1^>404 Not Found^</h1^>^</div^>^</template^>^<script^>export default {name: 'Error404'};^</script^> > src\views\error-page\404.vue
echo ^<template^>^<div^>^<h1^>401 Unauthorized^</h1^>^</div^>^</template^>^<script^>export default {name: 'Error401'};^</script^> > src\views\error-page\401.vue

echo Creating basic template for redirect...
echo ^<template^>^<div^>Redirecting...^</div^>^</template^>^<script^>export default {name: 'Redirect'};^</script^> > src\views\redirect\index.vue

echo Creating basic template for auth-redirect...
echo ^<template^>^<div^>Auth Redirect^</div^>^</template^>^<script^>export default {name: 'AuthRedirect'};^</script^> > src\views\login\auth-redirect.vue

echo Creating basic template for notification...
echo ^<template^>^<div^>^<h2^>Notifications^</h2^>^</div^>^</template^>^<script^>export default {name: 'Notification'};^</script^> > src\views\notification\index.vue

echo Fixed key Vue files!
